import React, { useState, useEffect } from 'react';
import { Button } from '../common';
import ConnectionList from '../connection/ConnectionList';
import DataBrowser from '../data/DataBrowser';
import SQLEditor from '../editor/SQLEditor';

interface Connection {
  id: string;
  name: string;
  type: string;
  host: string;
  port: number;
  database: string;
}

type ViewMode = 'connections' | 'browser' | 'editor';

const MainLayout: React.FC = () => {
  const [currentView, setCurrentView] = useState<ViewMode>('connections');
  const [selectedConnection, setSelectedConnection] = useState<Connection | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(false);

  // 检测屏幕尺寸
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setSidebarOpen(false);
      }
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // 处理连接选择
  const handleConnectionSelect = (connection: Connection) => {
    setSelectedConnection(connection);
    setCurrentView('browser');
    if (isMobile) {
      setSidebarOpen(false);
    }
  };

  // 导航菜单项
  const navigationItems = [
    {
      id: 'connections' as ViewMode,
      name: 'Connections',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
        </svg>
      ),
    },
    {
      id: 'browser' as ViewMode,
      name: 'Data Browser',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0" />
        </svg>
      ),
      disabled: !selectedConnection,
    },
    {
      id: 'editor' as ViewMode,
      name: 'SQL Editor',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
      ),
      disabled: !selectedConnection,
    },
  ];

  // 渲染当前视图
  const renderCurrentView = () => {
    switch (currentView) {
      case 'connections':
        return <ConnectionList onConnectionSelect={handleConnectionSelect} />;
      case 'browser':
        return selectedConnection ? (
          <DataBrowser connection={selectedConnection} />
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <p className="text-gray-500 dark:text-gray-400">
                Please select a connection first
              </p>
            </div>
          </div>
        );
      case 'editor':
        return selectedConnection ? (
          <SQLEditor connection={selectedConnection} />
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <p className="text-gray-500 dark:text-gray-400">
                Please select a connection first
              </p>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-dark-900">
      {/* 侧边栏 */}
      <div className={`
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        ${isMobile ? 'fixed inset-y-0 left-0 z-50 w-64' : 'relative w-64'}
        bg-white dark:bg-dark-800 border-r border-gray-200 dark:border-dark-600 transition-transform duration-300 ease-in-out
      `}>
        {/* 侧边栏头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-dark-600">
          <h1 className="text-xl font-bold text-gray-900 dark:text-gray-100">
            DB Manager
          </h1>
          {isMobile && (
            <button
              onClick={() => setSidebarOpen(false)}
              className="p-1 rounded-md text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>

        {/* 连接信息 */}
        {selectedConnection && (
          <div className="p-4 border-b border-gray-200 dark:border-dark-600 bg-gray-50 dark:bg-dark-700">
            <div className="text-sm">
              <div className="font-medium text-gray-900 dark:text-gray-100">
                {selectedConnection.name}
              </div>
              <div className="text-gray-500 dark:text-gray-400">
                {selectedConnection.type.toUpperCase()} • {selectedConnection.host}:{selectedConnection.port}
              </div>
            </div>
          </div>
        )}

        {/* 导航菜单 */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {navigationItems.map((item) => (
              <li key={item.id}>
                <button
                  onClick={() => !item.disabled && setCurrentView(item.id)}
                  disabled={item.disabled}
                  className={`
                    w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors
                    ${currentView === item.id
                      ? 'bg-primary-100 dark:bg-primary-900 text-primary-900 dark:text-primary-100'
                      : item.disabled
                        ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700'
                    }
                  `}
                >
                  <span className="mr-3">{item.icon}</span>
                  {item.name}
                </button>
              </li>
            ))}
          </ul>
        </nav>

        {/* 侧边栏底部 */}
        <div className="p-4 border-t border-gray-200 dark:border-dark-600">
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              v1.0.0
            </span>
            <button className="p-1 rounded-md text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* 主内容区 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* 顶部栏 */}
        <header className="bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-600 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* 移动端菜单按钮 */}
              {isMobile && (
                <button
                  onClick={() => setSidebarOpen(true)}
                  className="p-1 rounded-md text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                </button>
              )}

              {/* 面包屑导航 */}
              <nav className="flex items-center space-x-2 text-sm">
                <span className="text-gray-500 dark:text-gray-400">
                  {navigationItems.find(item => item.id === currentView)?.name}
                </span>
                {selectedConnection && (
                  <>
                    <span className="text-gray-400 dark:text-gray-600">/</span>
                    <span className="text-gray-900 dark:text-gray-100">
                      {selectedConnection.name}
                    </span>
                  </>
                )}
              </nav>
            </div>

            {/* 右侧操作 */}
            <div className="flex items-center space-x-2">
              <Button size="sm" variant="ghost">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </Button>
            </div>
          </div>
        </header>

        {/* 主内容 */}
        <main className="flex-1 overflow-hidden bg-gray-50 dark:bg-dark-900">
          {renderCurrentView()}
        </main>
      </div>

      {/* 移动端遮罩 */}
      {isMobile && sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default MainLayout;