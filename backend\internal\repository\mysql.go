package repository

import (
	"context"
	"database-manager/backend/internal/model"
	"database/sql"
	"fmt"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

// MySQLRepository MySQL连接器实现
type MySQLRepository struct {
	db        *sql.DB
	config    *model.ConnectionConfig
	connected bool
	tx        *sql.Tx // 事务对象
}

// NewMySQLRepository 创建MySQL连接器
func NewMySQLRepository() *MySQLRepository {
	return &MySQLRepository{
		connected: false,
	}
}

// Connect 连接MySQL数据库
func (m *MySQLRepository) Connect(ctx context.Context, config *model.ConnectionConfig) error {
	m.config = config

	// 构建DSN (Data Source Name)
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s",
		config.Username,
		config.Password,
		config.Host,
		config.Port,
		config.Database,
	)

	// 添加SSL配置
	if config.SSL {
		dsn += "?tls=true"
	} else {
		dsn += "?tls=false"
	}

	// 添加其他选项
	if config.Options != nil {
		params := []string{}
		for key, value := range config.Options {
			params = append(params, fmt.Sprintf("%s=%s", key, value))
		}
		if len(params) > 0 {
			if strings.Contains(dsn, "?") {
				dsn += "&" + strings.Join(params, "&")
			} else {
				dsn += "?" + strings.Join(params, "&")
			}
		}
	}

	// 打开数据库连接
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("failed to open MySQL connection: %w", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	m.db = db

	// 测试连接
	if err := m.Ping(ctx); err != nil {
		m.db.Close()
		return fmt.Errorf("failed to ping MySQL: %w", err)
	}

	m.connected = true
	return nil
}

// Disconnect 断开连接
func (m *MySQLRepository) Disconnect(ctx context.Context) error {
	if m.tx != nil {
		m.tx.Rollback()
		m.tx = nil
	}

	if m.db != nil {
		err := m.db.Close()
		m.db = nil
		m.connected = false
		return err
	}
	return nil
}

// Ping 测试连接
func (m *MySQLRepository) Ping(ctx context.Context) error {
	if m.db == nil {
		return fmt.Errorf("MySQL connection not initialized")
	}

	return m.db.PingContext(ctx)
}

// IsConnected 检查连接状态
func (m *MySQLRepository) IsConnected() bool {
	return m.connected && m.db != nil
}

// GetDatabaseInfo 获取数据库信息
func (m *MySQLRepository) GetDatabaseInfo(ctx context.Context) (*model.DatabaseInfo, error) {
	if !m.IsConnected() {
		return nil, fmt.Errorf("not connected to MySQL")
	}

	// 获取MySQL版本
	var version string
	err := m.db.QueryRowContext(ctx, "SELECT VERSION()").Scan(&version)
	if err != nil {
		version = "unknown"
	}

	// 获取数据库大小
	var size sql.NullInt64
	sizeQuery := `
		SELECT ROUND(SUM(data_length + index_length), 0) as size
		FROM information_schema.tables
		WHERE table_schema = ?
	`
	err = m.db.QueryRowContext(ctx, sizeQuery, m.config.Database).Scan(&size)
	if err != nil {
		size.Int64 = 0
	}

	// 获取表数量
	var tableCount int
	tableQuery := `
		SELECT COUNT(*)
		FROM information_schema.tables
		WHERE table_schema = ?
	`
	err = m.db.QueryRowContext(ctx, tableQuery, m.config.Database).Scan(&tableCount)
	if err != nil {
		tableCount = 0
	}

	return &model.DatabaseInfo{
		Name:    m.config.Database,
		Size:    size.Int64,
		Tables:  tableCount,
		Version: version,
	}, nil
}

// GetTables 获取表列表
func (m *MySQLRepository) GetTables(ctx context.Context, database string) ([]*model.TableInfo, error) {
	if !m.IsConnected() {
		return nil, fmt.Errorf("not connected to MySQL")
	}

	query := `
		SELECT
			table_name,
			table_type,
			IFNULL(table_rows, 0) as table_rows,
			IFNULL(data_length + index_length, 0) as table_size,
			IFNULL(table_comment, '') as table_comment
		FROM information_schema.tables
		WHERE table_schema = ?
		ORDER BY table_name
	`

	rows, err := m.db.QueryContext(ctx, query, database)
	if err != nil {
		return nil, fmt.Errorf("failed to query tables: %w", err)
	}
	defer rows.Close()

	var tables []*model.TableInfo
	for rows.Next() {
		var table model.TableInfo
		err := rows.Scan(
			&table.Name,
			&table.Type,
			&table.Rows,
			&table.Size,
			&table.Comment,
		)
		if err != nil {
			continue
		}
		tables = append(tables, &table)
	}

	return tables, nil
}