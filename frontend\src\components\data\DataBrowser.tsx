import React, { useState, useEffect } from 'react';
import { Button, Table, Input } from '../common';
import type { Column } from '../common';

interface Connection {
  id: string;
  name: string;
  type: string;
  host: string;
  port: number;
  database: string;
}

interface DatabaseInfo {
  name: string;
  tables?: string[];
  collections?: string[];
  keys?: string[];
  schemas?: string[];
}

interface TableData {
  columns: string[];
  rows: any[][];
  total: number;
}

interface DataBrowserProps {
  connection: Connection;
}

const DataBrowser: React.FC<DataBrowserProps> = ({ connection }) => {
  const [databaseInfo, setDatabaseInfo] = useState<DatabaseInfo | null>(null);
  const [selectedTable, setSelectedTable] = useState<string>('');
  const [tableData, setTableData] = useState<TableData | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(50);

  // 获取数据库信息
  const fetchDatabaseInfo = async () => {
    try {
      setLoading(true);
      // TODO: 调用API获取数据库信息
      // const response = await fetch(`/api/query/database-info?connection_id=${connection.id}`);
      // const data = await response.json();
      // setDatabaseInfo(data.data);

      // 模拟数据
      if (connection.type === 'mysql' || connection.type === 'postgresql') {
        setDatabaseInfo({
          name: connection.database,
          tables: ['users', 'products', 'orders', 'categories', 'reviews'],
        });
      } else if (connection.type === 'mongodb') {
        setDatabaseInfo({
          name: connection.database,
          collections: ['users', 'products', 'orders', 'logs'],
        });
      } else if (connection.type === 'redis') {
        setDatabaseInfo({
          name: connection.database,
          keys: ['user:1', 'user:2', 'session:abc123', 'cache:products', 'queue:emails'],
        });
      }
    } catch (error) {
      console.error('Failed to fetch database info:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取表数据
  const fetchTableData = async (tableName: string, page: number = 1) => {
    try {
      setLoading(true);
      // TODO: 调用API获取表数据
      // const response = await fetch(`/api/query/execute`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     connection_id: connection.id,
      //     query: `SELECT * FROM ${tableName} LIMIT ${pageSize} OFFSET ${(page - 1) * pageSize}`,
      //   }),
      // });
      // const data = await response.json();
      // setTableData(data.data);

      // 模拟数据
      const mockData = generateMockTableData(tableName);
      setTableData(mockData);
    } catch (error) {
      console.error('Failed to fetch table data:', error);
    } finally {
      setLoading(false);
    }
  };

  // 生成模拟表数据
  const generateMockTableData = (tableName: string): TableData => {
    const mockTables = {
      users: {
        columns: ['id', 'name', 'email', 'created_at', 'status'],
        rows: [
          [1, 'John Doe', '<EMAIL>', '2024-01-01 10:00:00', 'active'],
          [2, 'Jane Smith', '<EMAIL>', '2024-01-02 11:30:00', 'active'],
          [3, 'Bob Johnson', '<EMAIL>', '2024-01-03 09:15:00', 'inactive'],
        ],
      },
      products: {
        columns: ['id', 'name', 'price', 'category', 'stock'],
        rows: [
          [1, 'Laptop', 999.99, 'Electronics', 50],
          [2, 'Mouse', 29.99, 'Electronics', 200],
          [3, 'Keyboard', 79.99, 'Electronics', 150],
        ],
      },
      orders: {
        columns: ['id', 'user_id', 'total', 'status', 'created_at'],
        rows: [
          [1, 1, 1029.98, 'completed', '2024-01-05 14:30:00'],
          [2, 2, 29.99, 'pending', '2024-01-06 16:45:00'],
          [3, 1, 79.99, 'shipped', '2024-01-07 10:20:00'],
        ],
      },
    };

    return mockTables[tableName as keyof typeof mockTables] || {
      columns: ['id', 'data'],
      rows: [[1, 'No data available']],
      total: 1,
    };
  };

  useEffect(() => {
    fetchDatabaseInfo();
  }, [connection]);

  useEffect(() => {
    if (selectedTable) {
      fetchTableData(selectedTable, currentPage);
    }
  }, [selectedTable, currentPage]);

  // 处理表选择
  const handleTableSelect = (tableName: string) => {
    setSelectedTable(tableName);
    setCurrentPage(1);
  };

  // 过滤表/集合/键列表
  const getFilteredItems = () => {
    if (!databaseInfo) return [];

    const items = databaseInfo.tables || databaseInfo.collections || databaseInfo.keys || [];
    return items.filter(item =>
      item.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  // 转换表数据为Table组件格式
  const getTableColumns = (): Column[] => {
    if (!tableData) return [];

    return tableData.columns.map(col => ({
      key: col,
      title: col,
      render: (value) => {
        if (value === null) return <span className="text-gray-400">NULL</span>;
        if (typeof value === 'boolean') return value ? 'true' : 'false';
        if (typeof value === 'object') return JSON.stringify(value);
        return String(value);
      },
    }));
  };

  const getTableData = () => {
    if (!tableData) return [];

    return tableData.rows.map((row, index) => {
      const record: any = { _index: index };
      tableData.columns.forEach((col, colIndex) => {
        record[col] = row[colIndex];
      });
      return record;
    });
  };

  return (
    <div className="flex h-full">
      {/* 左侧边栏 - 数据库结构 */}
      <div className="w-64 bg-white dark:bg-dark-800 border-r border-gray-200 dark:border-dark-600 flex flex-col">
        <div className="p-4 border-b border-gray-200 dark:border-dark-600">
          <h3 className="font-medium text-gray-900 dark:text-gray-100">
            {connection.name}
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {connection.type.toUpperCase()} • {connection.database}
          </p>
        </div>

        <div className="p-4">
          <Input
            placeholder="Search..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            leftIcon={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            }
          />
        </div>

        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <div className="p-4 text-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mx-auto"></div>
            </div>
          ) : (
            <div className="space-y-1 p-2">
              {getFilteredItems().map((item) => (
                <button
                  key={item}
                  onClick={() => handleTableSelect(item)}
                  className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                    selectedTable === item
                      ? 'bg-primary-100 dark:bg-primary-900 text-primary-900 dark:text-primary-100'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700'
                  }`}
                >
                  <div className="flex items-center">
                    <span className="mr-2">
                      {connection.type === 'mongodb' ? '📄' :
                       connection.type === 'redis' ? '🔑' : '📋'}
                    </span>
                    {item}
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 右侧内容区 - 数据展示 */}
      <div className="flex-1 flex flex-col">
        {selectedTable ? (
          <>
            <div className="p-4 border-b border-gray-200 dark:border-dark-600 bg-white dark:bg-dark-800">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {selectedTable}
                </h2>
                <div className="flex space-x-2">
                  <Button size="sm" variant="ghost">
                    Refresh
                  </Button>
                  <Button size="sm" variant="ghost">
                    Export
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex-1 overflow-hidden">
              <Table
                columns={getTableColumns()}
                data={getTableData()}
                loading={loading}
                emptyText="No data found"
                className="h-full"
              />
            </div>

            {/* 分页 */}
            {tableData && tableData.rows.length > 0 && (
              <div className="p-4 border-t border-gray-200 dark:border-dark-600 bg-white dark:bg-dark-800">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    Showing {(currentPage - 1) * pageSize + 1} to {Math.min(currentPage * pageSize, tableData.rows.length)} of {tableData.rows.length} entries
                  </span>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="ghost"
                      disabled={currentPage === 1}
                      onClick={() => setCurrentPage(prev => prev - 1)}
                    >
                      Previous
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      disabled={tableData.rows.length < pageSize}
                      onClick={() => setCurrentPage(prev => prev + 1)}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-dark-900">
            <div className="text-center">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 1.79 4 4 4h8c2.21 0 4-1.79 4-4V7M4 7c0-2.21 1.79-4 4-4h8c2.21 0 4-1.79 4-4M4 7h16" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                No table selected
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Select a table from the sidebar to view its data
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DataBrowser;