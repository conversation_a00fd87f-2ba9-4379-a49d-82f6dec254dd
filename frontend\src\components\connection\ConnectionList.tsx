import React, { useState, useEffect } from 'react';
import { Button, Table, Modal } from '../common';
import type { Column } from '../common';
import ConnectionForm from './ConnectionForm';

interface Connection {
  id: string;
  name: string;
  type: string;
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  ssl: boolean;
  connected: boolean;
  created_at: string;
  updated_at: string;
}

interface ConnectionListProps {
  onConnectionSelect?: (connection: Connection) => void;
}

const ConnectionList: React.FC<ConnectionListProps> = ({ onConnectionSelect }) => {
  const [connections, setConnections] = useState<Connection[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingConnection, setEditingConnection] = useState<Connection | null>(null);

  // 获取连接列表
  const fetchConnections = async () => {
    try {
      setLoading(true);
      // TODO: 调用API获取连接列表
      const response = await fetch('/api/connections');
      const data = await response.json();
      setConnections(data.data || []);

      // 模拟数据
      // setConnections([
      //   {
      //     id: '1',
      //     name: 'Local Redis',
      //     type: 'redis',
      //     host: 'localhost',
      //     port: 6379,
      //     username: '',
      //     password: '',
      //     database: '0',
      //     ssl: false,
      //     connected: true,
      //     created_at: '2024-01-01T00:00:00Z',
      //     updated_at: '2024-01-01T00:00:00Z',
      //   },
      //   {
      //     id: '2',
      //     name: 'Production MySQL',
      //     type: 'mysql',
      //     host: 'prod-mysql.example.com',
      //     port: 3306,
      //     username: 'admin',
      //     password: 'password',
      //     database: 'app_db',
      //     ssl: true,
      //     connected: false,
      //     created_at: '2024-01-01T00:00:00Z',
      //     updated_at: '2024-01-01T00:00:00Z',
      //   },
      // ]);
    } catch (error) {
      console.error('Failed to fetch connections:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchConnections();
  }, []);

  // 删除连接
  const handleDelete = async (connection: Connection) => {
    if (!confirm(`Are you sure you want to delete "${connection.name}"?`)) {
      return;
    }

    try {
      // TODO: 调用API删除连接
      await fetch(`/api/connections?id=${connection.id}`, { method: 'DELETE' });

      // setConnections(prev => prev.filter(c => c.id !== connection.id));
    } catch (error) {
      console.error('Failed to delete connection:', error);
    }
  };

  // 测试连接
  const handleTest = async (connection: Connection) => {
    try {
      // TODO: 调用API测试连接
      const response = await fetch('/api/connections/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(connection),
      });

      alert('Connection test successful!');
    } catch (error) {
      console.error('Connection test failed:', error);
      alert('Connection test failed!');
    }
  };

  // 编辑连接
  const handleEdit = (connection: Connection) => {
    setEditingConnection(connection);
    setShowForm(true);
  };

  // 连接成功回调
  const handleConnectionSaved = () => {
    setShowForm(false);
    setEditingConnection(null);
    fetchConnections();
  };

  // 获取数据库类型图标
  const getDatabaseIcon = (type: string) => {
    const icons = {
      redis: '🔴',
      mysql: '🐬',
      postgresql: '🐘',
      mongodb: '🍃',
    };
    return icons[type as keyof typeof icons] || '💾';
  };

  // 获取连接状态
  const getConnectionStatus = (connected: boolean) => {
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        connected
          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
          : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      }`}>
        <span className={`w-1.5 h-1.5 mr-1.5 rounded-full ${
          connected ? 'bg-green-400' : 'bg-red-400'
        }`} />
        {connected ? 'Connected' : 'Disconnected'}
      </span>
    );
  };

  const columns: Column[] = [
    {
      key: 'name',
      title: 'Name',
      render: (value, record) => (
        <div className="flex items-center">
          <span className="text-lg mr-2">{getDatabaseIcon(record.type)}</span>
          <div>
            <div className="font-medium text-gray-900 dark:text-gray-100">{value}</div>
            <div className="text-sm text-gray-500 dark:text-gray-400">{record.type.toUpperCase()}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'host',
      title: 'Host',
      render: (value, record) => `${value}:${record.port}`,
    },
    {
      key: 'database',
      title: 'Database',
    },
    {
      key: 'connected',
      title: 'Status',
      align: 'center',
      render: (value) => getConnectionStatus(value),
    },
    {
      key: 'actions',
      title: 'Actions',
      align: 'right',
      render: (_, record) => (
        <div className="flex items-center space-x-2">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => handleTest(record)}
          >
            Test
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => handleEdit(record)}
          >
            Edit
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => handleDelete(record)}
          >
            Delete
          </Button>
          {onConnectionSelect && (
            <Button
              size="sm"
              variant="primary"
              onClick={() => onConnectionSelect(record)}
            >
              Connect
            </Button>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Database Connections
        </h2>
        <Button onClick={() => setShowForm(true)}>
          Add Connection
        </Button>
      </div>

      <Table
        columns={columns}
        data={connections}
        loading={loading}
        emptyText="No connections found. Click 'Add Connection' to create one."
        onRowClick={onConnectionSelect}
      />

      <Modal
        isOpen={showForm}
        onClose={() => {
          setShowForm(false);
          setEditingConnection(null);
        }}
        title={editingConnection ? 'Edit Connection' : 'Add Connection'}
        size="lg"
      >
        <ConnectionForm
          connection={editingConnection}
          onSave={handleConnectionSaved}
          onCancel={() => {
            setShowForm(false);
            setEditingConnection(null);
          }}
        />
      </Modal>
    </div>
  );
};

export default ConnectionList;