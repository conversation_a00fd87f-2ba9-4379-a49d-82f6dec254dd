{
  "common": {
    "save": "保存",
    "cancel": "取消",
    "delete": "删除",
    "edit": "编辑",
    "add": "添加",
    "test": "测试",
    "connect": "连接",
    "disconnect": "断开连接",
    "refresh": "刷新",
    "export": "导出",
    "import": "导入",
    "search": "搜索",
    "loading": "加载中...",
    "error": "错误",
    "success": "成功",
    "warning": "警告",
    "info": "信息",
    "confirm": "确认",
    "yes": "是",
    "no": "否",
    "ok": "确定",
    "close": "关闭",
    "back": "返回",
    "next": "下一步",
    "previous": "上一步",
    "clear": "清空",
    "reset": "重置"
  },
  "app": {
    "title": "数据库管理器",
    "description": "现代化数据库管理工具"
  },
  "navigation": {
    "connections": "数据库连接",
    "dataBrowser": "数据浏览器",
    "sqlEditor": "SQL编辑器",
    "settings": "设置"
  },
  "connection": {
    "title": "数据库连接",
    "addConnection": "添加连接",
    "editConnection": "编辑连接",
    "deleteConnection": "删除连接",
    "testConnection": "测试连接",
    "connectionName": "连接名称",
    "databaseType": "数据库类型",
    "host": "主机地址",
    "port": "端口",
    "username": "用户名",
    "password": "密码",
    "database": "数据库",
    "ssl": "启用SSL",
    "connected": "已连接",
    "disconnected": "未连接",
    "connectionTest": {
      "success": "连接测试成功！",
      "failed": "连接测试失败！",
      "testing": "正在测试连接..."
    },
    "validation": {
      "nameRequired": "连接名称不能为空",
      "typeRequired": "数据库类型不能为空",
      "hostRequired": "主机地址不能为空",
      "portRequired": "端口号必须有效",
      "databaseRequired": "数据库名称不能为空"
    },
    "deleteConfirm": "确定要删除连接 \"{name}\" 吗？",
    "noConnections": "暂无连接。点击"添加连接"创建新连接。"
  },
  "dataBrowser": {
    "title": "数据浏览器",
    "noTableSelected": "未选择表",
    "selectTablePrompt": "从侧边栏选择一个表来查看数据",
    "noData": "暂无数据",
    "showingEntries": "显示第 {start} 到 {end} 条，共 {total} 条记录"
  },
  "sqlEditor": {
    "title": "SQL编辑器",
    "placeholder": "在此输入SQL查询语句...",
    "execute": "执行",
    "executeShortcut": "执行 (Ctrl+Enter)",
    "noResults": "暂无结果",
    "executePrompt": "执行查询以查看结果",
    "result": "结果",
    "rowsAffected": "影响了 {count} 行",
    "executionTime": "执行时间：{time}毫秒",
    "queryError": "查询错误",
    "samples": {
      "selectUsers": "查询用户",
      "countRecords": "统计记录",
      "joinTables": "关联查询",
      "groupBy": "分组查询"
    }
  },
  "theme": {
    "light": "浅色",
    "dark": "深色",
    "system": "跟随系统",
    "switchTo": "切换到{theme}主题",
    "currentTheme": "当前主题：{theme}"
  },
  "language": {
    "english": "English",
    "chinese": "中文",
    "switchTo": "切换到{language}"
  },
  "errors": {
    "connectionFailed": "数据库连接失败",
    "queryFailed": "查询执行失败",
    "saveFailed": "保存失败",
    "loadFailed": "加载失败",
    "networkError": "网络错误",
    "unknownError": "发生未知错误"
  },
  "messages": {
    "connectionSaved": "连接保存成功",
    "connectionDeleted": "连接删除成功",
    "queryExecuted": "查询执行成功",
    "dataExported": "数据导出成功"
  },
  "settings": {
    "title": "设置",
    "export": "导出",
    "import": "导入",
    "reset": "重置",
    "importTitle": "导入设置",
    "importPlaceholder": "在此粘贴设置JSON...",
    "tabs": {
      "appearance": "外观",
      "editor": "编辑器",
      "database": "数据库",
      "general": "通用"
    },
    "appearance": {
      "title": "外观设置",
      "theme": "主题",
      "language": "语言",
      "fontSize": "字体大小",
      "sidebarWidth": "侧边栏宽度"
    },
    "editor": {
      "title": "编辑器设置",
      "showLineNumbers": "显示行号",
      "autoComplete": "自动完成",
      "wordWrap": "自动换行",
      "tabSize": "Tab大小"
    },
    "database": {
      "title": "数据库设置",
      "queryTimeout": "查询超时",
      "maxRows": "最大行数",
      "autoRefresh": "自动刷新",
      "refreshInterval": "刷新间隔"
    },
    "general": {
      "title": "通用设置",
      "confirmDelete": "确认删除",
      "showWelcome": "显示欢迎页",
      "autoSave": "自动保存"
    }
  }
}