package api

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"runtime/debug"
	"time"
)

// Middleware 中间件函数类型
type Middleware func(http.HandlerFunc) http.HandlerFunc

// LoggingMiddleware 日志记录中间件
func LoggingMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()

		// 创建响应写入器包装器来捕获状态码
		wrapper := &responseWriter{
			ResponseWriter: w,
			statusCode:     http.StatusOK,
		}

		// 执行下一个处理器
		next(wrapper, r)

		// 记录请求日志
		duration := time.Since(start)
		log.Printf("[%s] %s %s - %d - %v",
			r.Method,
			r.URL.Path,
			r.RemoteAddr,
			wrapper.statusCode,
			duration,
		)
	}
}

// CORSMiddleware CORS中间件
func CORSMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 设置CORS头
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
		w.Header().Set("Access-Control-Max-Age", "86400")

		// 处理预检请求
		if r.Method == http.MethodOptions {
			w.WriteHeader(http.StatusOK)
			return
		}

		// 执行下一个处理器
		next(w, r)
	}
}

// RecoveryMiddleware 恢复中间件，处理panic
func RecoveryMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		defer func() {
			if err := recover(); err != nil {
				// 记录panic信息
				log.Printf("Panic recovered: %v\n%s", err, debug.Stack())

				// 返回500错误
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusInternalServerError)

				response := APIResponse{
					Success: false,
					Error:   "Internal server error",
				}

				json.NewEncoder(w).Encode(response)
			}
		}()

		// 执行下一个处理器
		next(w, r)
	}
}

// ContentTypeMiddleware 内容类型中间件
func ContentTypeMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 对于POST、PUT、PATCH请求，检查Content-Type
		if r.Method == http.MethodPost || r.Method == http.MethodPut || r.Method == http.MethodPatch {
			contentType := r.Header.Get("Content-Type")
			if contentType != "application/json" && contentType != "" {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusUnsupportedMediaType)

				response := APIResponse{
					Success: false,
					Error:   "Content-Type must be application/json",
				}

				json.NewEncoder(w).Encode(response)
				return
			}
		}

		// 执行下一个处理器
		next(w, r)
	}
}

// RateLimitMiddleware 简单的速率限制中间件
func RateLimitMiddleware(requestsPerMinute int) Middleware {
	// 简单的内存存储，实际应用中应该使用Redis等
	clients := make(map[string][]time.Time)

	return func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			clientIP := getClientIP(r)
			now := time.Now()

			// 清理过期的请求记录
			if requests, exists := clients[clientIP]; exists {
				var validRequests []time.Time
				for _, reqTime := range requests {
					if now.Sub(reqTime) < time.Minute {
						validRequests = append(validRequests, reqTime)
					}
				}
				clients[clientIP] = validRequests
			}

			// 检查是否超过限制
			if len(clients[clientIP]) >= requestsPerMinute {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusTooManyRequests)

				response := APIResponse{
					Success: false,
					Error:   "Rate limit exceeded",
				}

				json.NewEncoder(w).Encode(response)
				return
			}

			// 记录当前请求
			clients[clientIP] = append(clients[clientIP], now)

			// 执行下一个处理器
			next(w, r)
		}
	}
}

// SecurityHeadersMiddleware 安全头中间件
func SecurityHeadersMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 设置安全头
		w.Header().Set("X-Content-Type-Options", "nosniff")
		w.Header().Set("X-Frame-Options", "DENY")
		w.Header().Set("X-XSS-Protection", "1; mode=block")
		w.Header().Set("Referrer-Policy", "strict-origin-when-cross-origin")

		// 执行下一个处理器
		next(w, r)
	}
}

// ChainMiddleware 链式组合多个中间件
func ChainMiddleware(middlewares ...Middleware) Middleware {
	return func(next http.HandlerFunc) http.HandlerFunc {
		for i := len(middlewares) - 1; i >= 0; i-- {
			next = middlewares[i](next)
		}
		return next
	}
}

// responseWriter 响应写入器包装器
type responseWriter struct {
	http.ResponseWriter
	statusCode int
}

func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}

// getClientIP 获取客户端IP地址
func getClientIP(r *http.Request) string {
	// 检查X-Forwarded-For头
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		return xff
	}

	// 检查X-Real-IP头
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return xri
	}

	// 使用RemoteAddr
	return r.RemoteAddr
}

// ValidateJSONMiddleware JSON验证中间件
func ValidateJSONMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 只对有body的请求进行验证
		if r.Method == http.MethodPost || r.Method == http.MethodPut || r.Method == http.MethodPatch {
			if r.ContentLength > 0 {
				// 尝试解析JSON
				var temp interface{}
				decoder := json.NewDecoder(r.Body)
				if err := decoder.Decode(&temp); err != nil {
					w.Header().Set("Content-Type", "application/json")
					w.WriteHeader(http.StatusBadRequest)

					response := APIResponse{
						Success: false,
						Error:   fmt.Sprintf("Invalid JSON: %v", err),
					}

					json.NewEncoder(w).Encode(response)
					return
				}

				// 重新创建body（因为已经被读取了）
				// 注意：这里简化处理，实际应用中需要更复杂的处理
			}
		}

		// 执行下一个处理器
		next(w, r)
	}
}