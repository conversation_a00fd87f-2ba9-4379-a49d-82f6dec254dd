package main

import (
	"context"
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"database-manager/internal/memory"

	"github.com/google/uuid"
	"github.com/wailsapp/wails/v2/pkg/runtime"
)

// App struct
type App struct {
	ctx           context.Context
	connections   map[string]*Connection
	mutex         sync.RWMutex
	memoryManager *memory.Manager
}

// Connection represents a database connection
type Connection struct {
	ID        string            `json:"id"`
	Name      string            `json:"name"`
	Type      string            `json:"type"`
	Host      string            `json:"host"`
	Port      int               `json:"port"`
	Username  string            `json:"username"`
	Password  string            `json:"password"`
	Database  string            `json:"database"`
	SSL       bool              `json:"ssl"`
	Options   map[string]string `json:"options"`
	CreatedAt time.Time         `json:"created_at"`
	UpdatedAt time.Time         `json:"updated_at"`
	Connected bool              `json:"connected"`
}

// QueryResult represents query execution result
type QueryResult struct {
	Columns []string `json:"columns"`
	Rows    [][]any  `json:"rows"`
	Count   int      `json:"count"`
	Error   string   `json:"error,omitempty"`
}

// NewApp creates a new App application struct
func NewApp() *App {
	app := &App{
		connections:   make(map[string]*Connection),
		memoryManager: memory.NewManager(),
	}

	// 添加清理函数
	app.memoryManager.AddCleanupFunc(func() {
		app.cleanupConnections()
	})

	return app
}

// startup is called when the app starts. The context passed
// in is saved and can be used to call the runtime methods
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx
	log.Println("Database Manager started successfully")

	// 加载示例连接
	a.loadSampleConnections()
}

// loadSampleConnections 加载示例连接
func (a *App) loadSampleConnections() {
	sampleConnections := []*Connection{
		{
			ID:        uuid.New().String(),
			Name:      "Local Redis",
			Type:      "redis",
			Host:      "localhost",
			Port:      6379,
			Username:  "",
			Password:  "",
			Database:  "0",
			SSL:       false,
			Options:   make(map[string]string),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Connected: false,
		},
		{
			ID:        uuid.New().String(),
			Name:      "Local MySQL",
			Type:      "mysql",
			Host:      "localhost",
			Port:      3306,
			Username:  "root",
			Password:  "",
			Database:  "test",
			SSL:       false,
			Options:   make(map[string]string),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Connected: false,
		},
	}

	a.mutex.Lock()
	defer a.mutex.Unlock()

	for _, conn := range sampleConnections {
		a.connections[conn.ID] = conn
	}
}

// Wails前端调用的方法

// GetConnections 获取所有连接
func (a *App) GetConnections() []*Connection {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	var result []*Connection
	for _, conn := range a.connections {
		result = append(result, conn)
	}

	return result
}

// SaveConnection 保存连接
func (a *App) SaveConnection(connectionData map[string]interface{}) error {
	conn, err := a.mapToConnection(connectionData)
	if err != nil {
		return err
	}

	// 如果没有ID，生成新的ID
	if conn.ID == "" {
		conn.ID = uuid.New().String()
		conn.CreatedAt = time.Now()
	}
	conn.UpdatedAt = time.Now()

	a.mutex.Lock()
	defer a.mutex.Unlock()

	a.connections[conn.ID] = conn
	return nil
}

// DeleteConnection 删除连接
func (a *App) DeleteConnection(connectionID string) error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	if _, exists := a.connections[connectionID]; !exists {
		return fmt.Errorf("connection not found")
	}

	delete(a.connections, connectionID)
	return nil
}

// TestConnection 测试连接
func (a *App) TestConnection(connectionData map[string]interface{}) error {
	// 简单的连接测试模拟
	conn, err := a.mapToConnection(connectionData)
	if err != nil {
		return err
	}

	// 模拟连接测试
	if conn.Host == "" || conn.Port <= 0 {
		return fmt.Errorf("invalid host or port")
	}

	log.Printf("Testing connection to %s:%d", conn.Host, conn.Port)
	return nil
}

// ExecuteQuery 执行查询
func (a *App) ExecuteQuery(connectionID, query string) (*QueryResult, error) {
	a.mutex.RLock()
	conn, exists := a.connections[connectionID]
	a.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("connection not found")
	}

	// 模拟查询执行
	log.Printf("Executing query on %s: %s", conn.Name, query)

	// 返回模拟结果
	return &QueryResult{
		Columns: []string{"id", "name", "value"},
		Rows: [][]any{
			{1, "Sample Row 1", "Value 1"},
			{2, "Sample Row 2", "Value 2"},
			{3, "Sample Row 3", "Value 3"},
		},
		Count: 3,
	}, nil
}

// 辅助方法

// mapToConnection 将map转换为Connection
func (a *App) mapToConnection(data map[string]interface{}) (*Connection, error) {
	conn := &Connection{
		Options: make(map[string]string),
	}

	if id, ok := data["id"].(string); ok {
		conn.ID = id
	}

	if name, ok := data["name"].(string); ok {
		conn.Name = name
	} else {
		return nil, fmt.Errorf("name is required")
	}

	if dbType, ok := data["type"].(string); ok {
		conn.Type = dbType
	} else {
		return nil, fmt.Errorf("type is required")
	}

	if host, ok := data["host"].(string); ok {
		conn.Host = host
	} else {
		return nil, fmt.Errorf("host is required")
	}

	if port, ok := data["port"].(float64); ok {
		conn.Port = int(port)
	} else {
		return nil, fmt.Errorf("port is required")
	}

	if username, ok := data["username"].(string); ok {
		conn.Username = username
	}

	if password, ok := data["password"].(string); ok {
		conn.Password = password
	}

	if database, ok := data["database"].(string); ok {
		conn.Database = database
	}

	if ssl, ok := data["ssl"].(bool); ok {
		conn.SSL = ssl
	}

	if options, ok := data["options"].(map[string]interface{}); ok {
		for k, v := range options {
			if str, ok := v.(string); ok {
				conn.Options[k] = str
			}
		}
	}

	return conn, nil
}

// WindowSettings represents window configuration
type WindowSettings struct {
	Width     int  `json:"width"`
	Height    int  `json:"height"`
	X         int  `json:"x"`
	Y         int  `json:"y"`
	Maximized bool `json:"maximized"`
	Minimized bool `json:"minimized"`
}

// GetWindowSettings 获取窗口设置
func (a *App) GetWindowSettings() WindowSettings {
	return WindowSettings{
		Width:     1200,
		Height:    800,
		X:         100,
		Y:         100,
		Maximized: false,
		Minimized: false,
	}
}

// SetWindowSize 设置窗口大小
func (a *App) SetWindowSize(width, height int) error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowSetSize(a.ctx, width, height)
	return nil
}

// SetWindowPosition 设置窗口位置
func (a *App) SetWindowPosition(x, y int) error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowSetPosition(a.ctx, x, y)
	return nil
}

// MinimizeWindow 最小化窗口
func (a *App) MinimizeWindow() error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowMinimise(a.ctx)
	return nil
}

// MaximizeWindow 最大化窗口
func (a *App) MaximizeWindow() error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowMaximise(a.ctx)
	return nil
}

// RestoreWindow 恢复窗口
func (a *App) RestoreWindow() error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowUnminimise(a.ctx)
	return nil
}

// ToggleMaximize 切换最大化状态
func (a *App) ToggleMaximize() error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowToggleMaximise(a.ctx)
	return nil
}

// SetWindowTitle 设置窗口标题
func (a *App) SetWindowTitle(title string) error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowSetTitle(a.ctx, title)
	return nil
}

// ShowWindow 显示窗口
func (a *App) ShowWindow() error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowShow(a.ctx)
	return nil
}

// HideWindow 隐藏窗口
func (a *App) HideWindow() error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowHide(a.ctx)
	return nil
}

// CenterWindow 居中窗口
func (a *App) CenterWindow() error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowCenter(a.ctx)
	return nil
}

// SetWindowAlwaysOnTop 设置窗口置顶
func (a *App) SetWindowAlwaysOnTop(alwaysOnTop bool) error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	if alwaysOnTop {
		runtime.WindowSetAlwaysOnTop(a.ctx, true)
	} else {
		runtime.WindowSetAlwaysOnTop(a.ctx, false)
	}
	return nil
}

// GetWindowState 获取窗口状态
func (a *App) GetWindowState() map[string]interface{} {
	return map[string]interface{}{
		"isMaximized": false,
		"isMinimized": false,
		"isVisible":   true,
		"isFocused":   true,
	}
}

// SystemTraySettings 系统托盘设置
type SystemTraySettings struct {
	Enabled           bool `json:"enabled"`
	MinimizeToTray    bool `json:"minimizeToTray"`
	CloseToTray       bool `json:"closeToTray"`
	StartMinimized    bool `json:"startMinimized"`
	ShowNotifications bool `json:"showNotifications"`
}

// GetSystemTraySettings 获取系统托盘设置
func (a *App) GetSystemTraySettings() SystemTraySettings {
	return SystemTraySettings{
		Enabled:           true,
		MinimizeToTray:    true,
		CloseToTray:       false,
		StartMinimized:    false,
		ShowNotifications: true,
	}
}

// SetSystemTraySettings 设置系统托盘配置
func (a *App) SetSystemTraySettings(settings SystemTraySettings) error {
	// 这里可以保存设置到配置文件
	log.Printf("System tray settings updated: %+v", settings)
	return nil
}

// ShowNotification 显示系统通知
func (a *App) ShowNotification(title, message string) error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	// 使用Wails的通知功能
	log.Printf("Notification: %s - %s", title, message)
	return nil
}

// MinimizeToTray 最小化到系统托盘
func (a *App) MinimizeToTray() error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowHide(a.ctx)
	a.ShowNotification("Database Manager", "应用已最小化到系统托盘")
	return nil
}

// RestoreFromTray 从系统托盘恢复
func (a *App) RestoreFromTray() error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowShow(a.ctx)
	runtime.WindowUnminimise(a.ctx)
	return nil
}

// QuitApplication 退出应用程序
func (a *App) QuitApplication() error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	// 清理资源
	a.mutex.Lock()
	defer a.mutex.Unlock()

	// 关闭所有数据库连接
	for id := range a.connections {
		if err := a.DisconnectDatabase(id); err != nil {
			log.Printf("Error disconnecting from database %s: %v", id, err)
		}
	}

	runtime.Quit(a.ctx)
	return nil
}

// GetTrayMenuItems 获取托盘菜单项
func (a *App) GetTrayMenuItems() []map[string]interface{} {
	return []map[string]interface{}{
		{
			"id":    "show",
			"label": "显示主窗口",
			"type":  "normal",
		},
		{
			"id":   "separator1",
			"type": "separator",
		},
		{
			"id":    "connections",
			"label": "连接管理",
			"type":  "normal",
		},
		{
			"id":    "settings",
			"label": "设置",
			"type":  "normal",
		},
		{
			"id":   "separator2",
			"type": "separator",
		},
		{
			"id":    "about",
			"label": "关于",
			"type":  "normal",
		},
		{
			"id":    "quit",
			"label": "退出",
			"type":  "normal",
		},
	}
}

// HandleTrayMenuClick 处理托盘菜单点击
func (a *App) HandleTrayMenuClick(menuID string) error {
	switch menuID {
	case "show":
		return a.RestoreFromTray()
	case "connections":
		a.RestoreFromTray()
		// 这里可以触发前端导航到连接管理页面
		return nil
	case "settings":
		a.RestoreFromTray()
		// 这里可以触发前端导航到设置页面
		return nil
	case "about":
		return a.ShowNotification("Database Manager", "版本 1.0.0\n跨平台数据库管理工具")
	case "quit":
		return a.QuitApplication()
	default:
		return fmt.Errorf("unknown menu item: %s", menuID)
	}
}

// FileAssociation 文件关联信息
type FileAssociation struct {
	Extension   string `json:"extension"`
	Description string `json:"description"`
	IconPath    string `json:"iconPath"`
	MimeType    string `json:"mimeType"`
}

// GetSupportedFileTypes 获取支持的文件类型
func (a *App) GetSupportedFileTypes() []FileAssociation {
	return []FileAssociation{
		{
			Extension:   ".sql",
			Description: "SQL Script File",
			IconPath:    "sql-icon",
			MimeType:    "text/sql",
		},
		{
			Extension:   ".dbm",
			Description: "Database Manager Configuration File",
			IconPath:    "config-icon",
			MimeType:    "application/json",
		},
	}
}

// OpenFile 打开文件
func (a *App) OpenFile(filePath string) error {
	if filePath == "" {
		return fmt.Errorf("file path is empty")
	}

	log.Printf("Opening file: %s", filePath)

	// 根据文件扩展名处理不同类型的文件
	switch {
	case strings.HasSuffix(strings.ToLower(filePath), ".sql"):
		return a.openSQLFile(filePath)
	case strings.HasSuffix(strings.ToLower(filePath), ".dbm"):
		return a.openConfigFile(filePath)
	default:
		return fmt.Errorf("unsupported file type: %s", filePath)
	}
}

// openSQLFile 打开SQL文件
func (a *App) openSQLFile(filePath string) error {
	// 这里可以读取SQL文件内容并在编辑器中打开
	log.Printf("Opening SQL file: %s", filePath)

	// 显示主窗口
	if err := a.RestoreFromTray(); err != nil {
		return err
	}

	// 这里可以触发前端打开SQL编辑器并加载文件内容
	return nil
}

// openConfigFile 打开配置文件
func (a *App) openConfigFile(filePath string) error {
	// 这里可以导入配置文件
	log.Printf("Opening config file: %s", filePath)

	// 显示主窗口
	if err := a.RestoreFromTray(); err != nil {
		return err
	}

	// 这里可以触发前端导入配置
	return nil
}

// RegisterFileAssociations 注册文件关联
func (a *App) RegisterFileAssociations() error {
	// 这个方法在安装时调用，用于注册文件关联
	log.Println("Registering file associations...")

	// 在实际实现中，这里会调用平台特定的API来注册文件关联
	// Windows: 修改注册表
	// macOS: 更新Info.plist
	// Linux: 更新.desktop文件和MIME类型

	return nil
}

// UnregisterFileAssociations 取消文件关联
func (a *App) UnregisterFileAssociations() error {
	// 这个方法在卸载时调用，用于清理文件关联
	log.Println("Unregistering file associations...")

	return nil
}

// HandleCommandLineArgs 处理命令行参数
func (a *App) HandleCommandLineArgs(args []string) error {
	for i, arg := range args {
		switch arg {
		case "--file", "-f":
			if i+1 < len(args) {
				return a.OpenFile(args[i+1])
			}
		case "--config", "-c":
			if i+1 < len(args) {
				return a.openConfigFile(args[i+1])
			}
		case "--edit", "-e":
			if i+1 < len(args) {
				return a.openSQLFile(args[i+1])
			}
		case "--new-connection":
			// 打开新建连接对话框
			return a.RestoreFromTray()
		case "--import-config":
			// 打开导入配置对话框
			return a.RestoreFromTray()
		case "--directory", "-d":
			if i+1 < len(args) {
				log.Printf("Opening directory: %s", args[i+1])
				return a.RestoreFromTray()
			}
		}
	}

	return nil
}

// cleanupConnections 清理连接
func (a *App) cleanupConnections() {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	for id, conn := range a.connections {
		if conn.Connected {
			log.Printf("Cleaning up connection: %s", id)
			// 这里可以添加具体的连接清理逻辑
			conn.Connected = false
		}
	}
}

// GetMemoryStats 获取内存统计信息
func (a *App) GetMemoryStats() map[string]interface{} {
	if a.memoryManager == nil {
		return map[string]interface{}{
			"error": "Memory manager not initialized",
		}
	}

	stats := a.memoryManager.GetStats()
	usage := a.memoryManager.GetMemoryUsage()
	gcStats := a.memoryManager.GetGCStats()

	return map[string]interface{}{
		"stats":   stats,
		"usage":   usage,
		"gcStats": gcStats,
	}
}

// ForceGarbageCollection 强制垃圾回收
func (a *App) ForceGarbageCollection() error {
	if a.memoryManager == nil {
		return fmt.Errorf("memory manager not initialized")
	}

	a.memoryManager.ForceGC()
	return nil
}

// SetMemoryThreshold 设置内存阈值
func (a *App) SetMemoryThreshold(thresholdMB int) error {
	if a.memoryManager == nil {
		return fmt.Errorf("memory manager not initialized")
	}

	threshold := uint64(thresholdMB) * 1024 * 1024 // 转换为字节
	a.memoryManager.SetMemoryThreshold(threshold)
	return nil
}

// SetGCInterval 设置GC检查间隔
func (a *App) SetGCInterval(intervalMinutes int) error {
	if a.memoryManager == nil {
		return fmt.Errorf("memory manager not initialized")
	}

	interval := time.Duration(intervalMinutes) * time.Minute
	a.memoryManager.SetGCInterval(interval)
	return nil
}

// OptimizeMemory 优化内存使用
func (a *App) OptimizeMemory() error {
	if a.memoryManager == nil {
		return fmt.Errorf("memory manager not initialized")
	}

	// 清理未使用的连接
	a.cleanupConnections()

	// 强制垃圾回收
	a.memoryManager.ForceGC()

	log.Println("Memory optimization completed")
	return nil
}

// shutdown 应用关闭时的清理
func (a *App) shutdown() {
	log.Println("Shutting down application...")

	// 关闭内存管理器
	if a.memoryManager != nil {
		a.memoryManager.Shutdown()
	}

	// 清理连接
	a.cleanupConnections()

	log.Println("Application shutdown completed")
}
