package main

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/google/uuid"
)

// App struct
type App struct {
	ctx         context.Context
	connections map[string]*Connection
	mutex       sync.RWMutex
}

// Connection represents a database connection
type Connection struct {
	ID        string            `json:"id"`
	Name      string            `json:"name"`
	Type      string            `json:"type"`
	Host      string            `json:"host"`
	Port      int               `json:"port"`
	Username  string            `json:"username"`
	Password  string            `json:"password"`
	Database  string            `json:"database"`
	SSL       bool              `json:"ssl"`
	Options   map[string]string `json:"options"`
	CreatedAt time.Time         `json:"created_at"`
	UpdatedAt time.Time         `json:"updated_at"`
	Connected bool              `json:"connected"`
}

// QueryResult represents query execution result
type QueryResult struct {
	Columns []string `json:"columns"`
	Rows    [][]any  `json:"rows"`
	Count   int      `json:"count"`
	Error   string   `json:"error,omitempty"`
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{
		connections: make(map[string]*Connection),
	}
}

// startup is called when the app starts. The context passed
// in is saved and can be used to call the runtime methods
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx
	log.Println("Database Manager started successfully")

	// 加载示例连接
	a.loadSampleConnections()
}

// loadSampleConnections 加载示例连接
func (a *App) loadSampleConnections() {
	sampleConnections := []*Connection{
		{
			ID:        uuid.New().String(),
			Name:      "Local Redis",
			Type:      "redis",
			Host:      "localhost",
			Port:      6379,
			Username:  "",
			Password:  "",
			Database:  "0",
			SSL:       false,
			Options:   make(map[string]string),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Connected: false,
		},
		{
			ID:        uuid.New().String(),
			Name:      "Local MySQL",
			Type:      "mysql",
			Host:      "localhost",
			Port:      3306,
			Username:  "root",
			Password:  "",
			Database:  "test",
			SSL:       false,
			Options:   make(map[string]string),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Connected: false,
		},
	}

	a.mutex.Lock()
	defer a.mutex.Unlock()

	for _, conn := range sampleConnections {
		a.connections[conn.ID] = conn
	}
}

// Wails前端调用的方法

// GetConnections 获取所有连接
func (a *App) GetConnections() []*Connection {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	var result []*Connection
	for _, conn := range a.connections {
		result = append(result, conn)
	}

	return result
}

// SaveConnection 保存连接
func (a *App) SaveConnection(connectionData map[string]interface{}) error {
	conn, err := a.mapToConnection(connectionData)
	if err != nil {
		return err
	}

	// 如果没有ID，生成新的ID
	if conn.ID == "" {
		conn.ID = uuid.New().String()
		conn.CreatedAt = time.Now()
	}
	conn.UpdatedAt = time.Now()

	a.mutex.Lock()
	defer a.mutex.Unlock()

	a.connections[conn.ID] = conn
	return nil
}

// DeleteConnection 删除连接
func (a *App) DeleteConnection(connectionID string) error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	if _, exists := a.connections[connectionID]; !exists {
		return fmt.Errorf("connection not found")
	}

	delete(a.connections, connectionID)
	return nil
}

// TestConnection 测试连接
func (a *App) TestConnection(connectionData map[string]interface{}) error {
	// 简单的连接测试模拟
	conn, err := a.mapToConnection(connectionData)
	if err != nil {
		return err
	}

	// 模拟连接测试
	if conn.Host == "" || conn.Port <= 0 {
		return fmt.Errorf("invalid host or port")
	}

	log.Printf("Testing connection to %s:%d", conn.Host, conn.Port)
	return nil
}

// ExecuteQuery 执行查询
func (a *App) ExecuteQuery(connectionID, query string) (*QueryResult, error) {
	a.mutex.RLock()
	conn, exists := a.connections[connectionID]
	a.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("connection not found")
	}

	// 模拟查询执行
	log.Printf("Executing query on %s: %s", conn.Name, query)

	// 返回模拟结果
	return &QueryResult{
		Columns: []string{"id", "name", "value"},
		Rows: [][]any{
			{1, "Sample Row 1", "Value 1"},
			{2, "Sample Row 2", "Value 2"},
			{3, "Sample Row 3", "Value 3"},
		},
		Count: 3,
	}, nil
}

// 辅助方法

// mapToConnection 将map转换为Connection
func (a *App) mapToConnection(data map[string]interface{}) (*Connection, error) {
	conn := &Connection{
		Options: make(map[string]string),
	}

	if id, ok := data["id"].(string); ok {
		conn.ID = id
	}

	if name, ok := data["name"].(string); ok {
		conn.Name = name
	} else {
		return nil, fmt.Errorf("name is required")
	}

	if dbType, ok := data["type"].(string); ok {
		conn.Type = dbType
	} else {
		return nil, fmt.Errorf("type is required")
	}

	if host, ok := data["host"].(string); ok {
		conn.Host = host
	} else {
		return nil, fmt.Errorf("host is required")
	}

	if port, ok := data["port"].(float64); ok {
		conn.Port = int(port)
	} else {
		return nil, fmt.Errorf("port is required")
	}

	if username, ok := data["username"].(string); ok {
		conn.Username = username
	}

	if password, ok := data["password"].(string); ok {
		conn.Password = password
	}

	if database, ok := data["database"].(string); ok {
		conn.Database = database
	}

	if ssl, ok := data["ssl"].(bool); ok {
		conn.SSL = ssl
	}

	if options, ok := data["options"].(map[string]interface{}); ok {
		for k, v := range options {
			if str, ok := v.(string); ok {
				conn.Options[k] = str
			}
		}
	}

	return conn, nil
}
