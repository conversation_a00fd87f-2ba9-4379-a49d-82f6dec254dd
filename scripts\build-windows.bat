@echo off
echo Building Database Manager for Windows...

REM 检查Wails是否安装
wails version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Wails is not installed. Please install Wails first.
    echo Run: go install github.com/wailsapp/wails/v2/cmd/wails@latest
    exit /b 1
)

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed. Please install Node.js first.
    exit /b 1
)

REM 检查Go是否安装
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Go is not installed. Please install Go first.
    exit /b 1
)

echo Cleaning previous builds...
if exist "build\bin" rmdir /s /q "build\bin"

echo Installing frontend dependencies...
cd frontend
call npm install
if %errorlevel% neq 0 (
    echo Error: Failed to install frontend dependencies
    exit /b 1
)

echo Building frontend...
call npm run build
if %errorlevel% neq 0 (
    echo Error: Failed to build frontend
    exit /b 1
)

cd ..

echo Building Wails application...
wails build -platform windows/amd64
if %errorlevel% neq 0 (
    echo Error: Failed to build Wails application
    exit /b 1
)

echo Build completed successfully!
echo Executable location: build\bin\database-manager.exe

pause