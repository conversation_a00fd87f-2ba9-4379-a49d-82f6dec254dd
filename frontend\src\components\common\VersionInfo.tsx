import React, { useState, useEffect } from 'react';
import { Button } from './Button';
import { Modal } from './Modal';

interface VersionInfo {
  version: string;
  buildTime: string;
  gitCommit: string;
  gitBranch: string;
  goVersion: string;
  platform: string;
  architecture: string;
  fullString: string;
}

interface UpdateInfo {
  available: boolean;
  latestVersion: string;
  currentVersion: string;
  releaseNotes: string;
  downloadUrl: string;
  releaseDate: string;
  critical: boolean;
}

interface VersionInfoProps {
  isOpen: boolean;
  onClose: () => void;
}

const VersionInfo: React.FC<VersionInfoProps> = ({ isOpen, onClose }) => {
  const [versionInfo, setVersionInfo] = useState<VersionInfo | null>(null);
  const [updateInfo, setUpdateInfo] = useState<UpdateInfo | null>(null);
  const [isCheckingUpdate, setIsCheckingUpdate] = useState(false);
  const [updateError, setUpdateError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      loadVersionInfo();
    }
  }, [isOpen]);

  const loadVersionInfo = async () => {
    try {
      // 这里调用后端API获取版本信息
      const version = await window.go.main.App.GetVersion();
      setVersionInfo(version);
    } catch (error) {
      console.error('Failed to load version info:', error);
    }
  };

  const checkForUpdates = async () => {
    setIsCheckingUpdate(true);
    setUpdateError(null);
    
    try {
      // 这里调用后端API检查更新
      const update = await window.go.main.App.CheckForUpdates();
      setUpdateInfo(update);
    } catch (error) {
      setUpdateError(error instanceof Error ? error.message : 'Failed to check for updates');
    } finally {
      setIsCheckingUpdate(false);
    }
  };

  const openDownloadUrl = () => {
    if (updateInfo?.downloadUrl) {
      window.open(updateInfo.downloadUrl, '_blank');
    }
  };

  const formatCommit = (commit: string) => {
    return commit.length > 8 ? commit.substring(0, 8) : commit;
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="关于 Database Manager"
      size="lg"
    >
      <div className="space-y-6">
        {/* 应用信息 */}
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
            <svg
              className="w-8 h-8 text-blue-600 dark:text-blue-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"
              />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Database Manager
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            跨平台数据库管理工具
          </p>
        </div>

        {/* 版本信息 */}
        {versionInfo && (
          <div className="bg-gray-50 dark:bg-dark-700 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
              版本信息
            </h3>
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div>
                <span className="text-gray-600 dark:text-gray-400">版本:</span>
                <span className="ml-2 font-mono text-gray-900 dark:text-gray-100">
                  {versionInfo.version}
                </span>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">平台:</span>
                <span className="ml-2 font-mono text-gray-900 dark:text-gray-100">
                  {versionInfo.platform}/{versionInfo.architecture}
                </span>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">构建时间:</span>
                <span className="ml-2 font-mono text-gray-900 dark:text-gray-100">
                  {formatDate(versionInfo.buildTime)}
                </span>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">Go版本:</span>
                <span className="ml-2 font-mono text-gray-900 dark:text-gray-100">
                  {versionInfo.goVersion}
                </span>
              </div>
              <div className="col-span-2">
                <span className="text-gray-600 dark:text-gray-400">Git提交:</span>
                <span className="ml-2 font-mono text-gray-900 dark:text-gray-100">
                  {formatCommit(versionInfo.gitCommit)} ({versionInfo.gitBranch})
                </span>
              </div>
            </div>
          </div>
        )}

        {/* 更新检查 */}
        <div className="border-t border-gray-200 dark:border-dark-600 pt-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              更新检查
            </h3>
            <Button
              variant="outline"
              size="sm"
              onClick={checkForUpdates}
              disabled={isCheckingUpdate}
            >
              {isCheckingUpdate ? '检查中...' : '检查更新'}
            </Button>
          </div>

          {updateError && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3 mb-4">
              <p className="text-red-700 dark:text-red-300 text-sm">
                {updateError}
              </p>
            </div>
          )}

          {updateInfo && (
            <div className={`rounded-lg p-4 ${
              updateInfo.available
                ? updateInfo.critical
                  ? 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
                  : 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
                : 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
            }`}>
              {updateInfo.available ? (
                <div>
                  <div className="flex items-center mb-2">
                    <svg
                      className={`w-5 h-5 mr-2 ${
                        updateInfo.critical ? 'text-red-600' : 'text-blue-600'
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"
                      />
                    </svg>
                    <span className={`font-semibold ${
                      updateInfo.critical ? 'text-red-700' : 'text-blue-700'
                    }`}>
                      {updateInfo.critical ? '重要更新可用' : '新版本可用'}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    版本 {updateInfo.latestVersion} 已发布
                    {updateInfo.releaseDate && (
                      <span className="ml-1">
                        ({formatDate(updateInfo.releaseDate)})
                      </span>
                    )}
                  </p>
                  {updateInfo.releaseNotes && (
                    <div className="mb-3">
                      <details className="text-sm">
                        <summary className="cursor-pointer text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100">
                          查看更新说明
                        </summary>
                        <div className="mt-2 p-2 bg-white dark:bg-dark-800 rounded border text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
                          {updateInfo.releaseNotes}
                        </div>
                      </details>
                    </div>
                  )}
                  <Button
                    variant={updateInfo.critical ? 'danger' : 'primary'}
                    size="sm"
                    onClick={openDownloadUrl}
                    disabled={!updateInfo.downloadUrl}
                  >
                    下载更新
                  </Button>
                </div>
              ) : (
                <div className="flex items-center">
                  <svg
                    className="w-5 h-5 text-green-600 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  <span className="text-green-700 dark:text-green-300 font-semibold">
                    您使用的是最新版本
                  </span>
                </div>
              )}
            </div>
          )}
        </div>

        {/* 链接 */}
        <div className="border-t border-gray-200 dark:border-dark-600 pt-4">
          <div className="flex flex-wrap gap-4 text-sm">
            <a
              href="https://github.com/database-manager/database-manager"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 dark:text-blue-400 hover:underline"
            >
              GitHub
            </a>
            <a
              href="https://github.com/database-manager/database-manager/issues"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 dark:text-blue-400 hover:underline"
            >
              报告问题
            </a>
            <a
              href="https://github.com/database-manager/database-manager/blob/main/LICENSE"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 dark:text-blue-400 hover:underline"
            >
              许可证
            </a>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default VersionInfo;
